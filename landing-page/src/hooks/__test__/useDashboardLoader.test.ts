import { renderHook, act } from '@testing-library/react';
import { useDashboardLoader } from '../useDashboardLoader';

jest.useFakeTimers();

describe('useDashboardLoader', () => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  it('should return false as initial state', () => {
    const { result } = renderHook(() =>
      useDashboardLoader({
        hasAtleastOneSubscription: false,
        tableauTokens: [],
      })
    );

    expect(result.current).toBe(false);
  });

  it('should set loading to false when tableauTokens includes error', () => {
    const { result } = renderHook(() =>
      useDashboardLoader({
        hasAtleastOneSubscription: false,
        tableauTokens: ['error'],
      })
    );

    expect(result.current).toBe(false);
  });

  it('should handle default case', () => {
    const { result } = renderHook(() =>
      useDashboardLoader({
        hasAtleastOneSubscription: false,
        tableauTokens: ['token'],
      })
    );

    expect(result.current).toBe(false);
  });

  it('should cleanup timer when unmounted', () => {
    const { unmount } = renderHook(() =>
      useDashboardLoader({
        hasAtleastOneSubscription: true,
        tableauTokens: [],
      })
    );

    unmount();
    
    // Advancing timer after unmount should not cause any warnings/errors
    act(() => {
      jest.advanceTimersByTime(2500);
    });
  });

  it('should update loading state when props change', () => {
    const { result, rerender } = renderHook(
      ({ hasAtleastOneSubscription, tableauTokens }) =>
        useDashboardLoader({ hasAtleastOneSubscription, tableauTokens }),
      {
        initialProps: {
          hasAtleastOneSubscription: false,
          tableauTokens: [],
        },
      }
    );

    expect(result.current).toBe(false);

    rerender({
      hasAtleastOneSubscription: true,
      tableauTokens: [],
    });

    expect(result.current).toBe(true);

    act(() => {
      jest.advanceTimersByTime(2500);
    });

    expect(result.current).toBe(false);
  });
});