import React from 'react';
import {
  render, screen, fireEvent, waitFor,
} from '@testing-library/react';
import CardModuleContainer from '../dashboard/iCard/CardModuleContainer';
import { useInfiniteQuery } from '../../hooks/useInfiniteQuery';
import { WidgetConstant } from '../dashboard/iCard/widget.constant';

jest.mock('../../hooks/useInfiniteQuery');

jest.mock('@paris2/styleguide', () => ({
  // eslint-disable-next-line max-len
  CardModule: ({
    title,
    onVesselClick,
  }: {
    title: string;
    onVesselClick: (vessel: { vessel_ownership_id: string }) => void;
  }) => (
    <div>
      <h1>{title}</h1>
      <div data-testid="columns-container" />
      <div data-testid="loading-indicator" />
      <button
        type="button"
        data-testid="vessel-click-button"
        onClick={() => onVesselClick({ vessel_ownership_id: '123' })}
      >
        Click Vessel
      </button>
    </div>
  ),
}));

jest.mock('../dashboard/iCard/CardModuleConfig', () => ({
  cardModuleConfigs: {
    'test-key': {
      title: 'Test Card',
      fetchFn1: jest.fn(),
      multiVesselSelects: [],
      columns: [],
    },
    deficiencies: {
      title: 'Deficiencies',
      fetchFn1: jest.fn(),
      multiVesselSelects: [
        {
          placeholder: 'All Vessels',
          width: '300px',
          groups: jest.fn(), // Pass the fetch function
          isSearchBoxVisible: true,
          isSelectAllVisible: true,
        },
      ],
      columns: [],
      staticData: {
        tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
        tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
        badgeColors: ['red', 'blue', 'green'],
      },
      visibleConfig: {
        IsiconRenderVisible: false,
        IsenLargeIconVisible: true,
        IsVesselSelectVisible: true,
        IsAlltabsVisible: false,
        IsLastUpdatedVisible: true,
        IsRefereshIconVisible: false,
        IsActionColumnVisible: true,
        vesselSelectPosition: 'before',
      },
      componentView: {
        gridComponent: 'bar',
        defaultComponent: 'list',
      },
      sizeKey: 'md',
    },
    'risk-assessment': {
      title: 'Risk Assessment',
      fetchFn1: jest.fn(),
      multiVesselSelects: [],
      columns: [],
    },
  },
}));

const mockUseInfiniteQuery = useInfiniteQuery as jest.Mock;

describe('CardModuleContainer', () => {
  beforeEach(() => {
    mockUseInfiniteQuery.mockReturnValue({
      data: {
        data: [],
        pagination: {
          totalItems: 0,
          totalPages: 0,
          page: 0,
          pageSize: 0,
        },
      },
      isLoading: false,
      isFetchingNextPage: false,
      fetchNextPage: jest.fn(),
      refetch: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders error message for invalid config key', () => {
    render(<CardModuleContainer configKey="invalid-key" />);
    expect(screen.getByText(/Error: Invalid configuration key provided/i)).toBeInTheDocument();
  });

  test('renders CardModule with valid config', () => {
    render(<CardModuleContainer configKey="test-key" />);
    expect(screen.queryByText(/Error:/i)).not.toBeInTheDocument();
  });

  test('updates stat card data for deficiencies widget', async () => {
    const mockOnStatCardDataUpdate = jest.fn();
    mockUseInfiniteQuery.mockReturnValue({
      data: {
        data: [{ overdue: '5' }, { overdue: '3' }],
        pagination: {},
      },
      isLoading: false,
    });

    render(
      <CardModuleContainer
        configKey={WidgetConstant.DEFICIENCIES}
        onStatCardDataUpdate={mockOnStatCardDataUpdate}
      />,
    );

    await waitFor(() => {
      expect(mockOnStatCardDataUpdate).toHaveBeenCalledWith({
        count: 8,
        isLoading: false,
      });
    });
  });

  test('handles vessel click correctly', () => {
    const mockWindow = window.open;
    window.open = jest.fn();

    render(<CardModuleContainer configKey="test-key" />);

    const vessel = { vessel_ownership_id: '123' };
    const component = screen.getByTestId('vessel-click-button');
    fireEvent.click(component, { vessel });

    expect(window.open).toHaveBeenCalledWith(
      'https://paris2-dev2.fleetship.com/vessel/ownership/details/123',
      '_blank',
    );

    window.open = mockWindow;
  });

  test('handles loading state for dropdowns', async () => {
    const mockOnStatCardDataUpdate = jest.fn().mockResolvedValue([{ id: 1, vessels: [] }]);
    render(
      <CardModuleContainer configKey="test-key" onStatCardDataUpdate={mockOnStatCardDataUpdate} />,
    );

    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });

  test('memoizes columns correctly', () => {
    const { rerender } = render(<CardModuleContainer configKey="test-key" />);
    const firstRender = screen.getByTestId('columns-container');

    rerender(<CardModuleContainer configKey="test-key" />);
    const secondRender = screen.getByTestId('columns-container');

    expect(firstRender).toBe(secondRender);
  });

  test('handles risk assessment stat updates correctly', async () => {
    const mockOnStatCardDataUpdate = jest.fn();
    mockUseInfiniteQuery.mockReturnValue({
      data: {
        data: [{ ra_level: 'Unassigned' }, { ra_level: 'Unassigned' }, { ra_level: 'Assigned' }],
        pagination: {},
      },
      isLoading: false,
    });

    render(
      <CardModuleContainer
        configKey={WidgetConstant.RISK_ASSESSMENT}
        onStatCardDataUpdate={mockOnStatCardDataUpdate}
      />,
    );

    await waitFor(() => {
      expect(mockOnStatCardDataUpdate).toHaveBeenCalledWith({
        count: 2,
        isLoading: false,
      });
    });
  });
});
