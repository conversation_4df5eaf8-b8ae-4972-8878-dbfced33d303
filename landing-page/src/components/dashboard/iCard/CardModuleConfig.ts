import { WidgetConstant } from './widget.constant';
import {
  fetchVesselOwnerships,
  getDeficienciesForTable,
  getItinerariesForTable,
  getOfrStatsForTable,
  getSurveysAndCertsForTable,
  getVesselsRA,
} from '../../../services/vm-widget-service';
import {
  getDefiniciencyColumns,
  getRiskAssessmentColumns,
  getItineraryColumns,
  getOfrColumns,
  getSurveysAndCertsColumns,
} from './ColumnConfig';
import { vesselGroups2 } from 'src/services/__mocks__/card-module-config';
import ItineraryListItem from './CardResponsive/ItineraryListItem';
import RiskAssessmentListItem from './CardResponsive/RiskAssessment';

export const cardModuleConfigs: { [key: string]: any } = {
  [WidgetConstant.OWNER_FINANCIAL_REPORTING]: {
    title: 'Owner Financial Reporting',
    fetchFn1: getOfrStatsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getOfrColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_code',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.ITINERARY_ETA]: {
    title: 'Itinerary (ETA)',
    fetchFn1: getItinerariesForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px', // 200 or 300 or 350 should need to handle with an enum or interface to allow only these values
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getItineraryColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
    responsive: true,
    responsiveConfig: {
      designName: 'card-list-1',
      component: ItineraryListItem
    },
    // responsiveCardContainerHeight: '710px',
    // responsiveCardListContainerHeight: '580px'
  },

  [WidgetConstant.RISK_ASSESSMENT]: {
    title: 'Risk Assessment',
    fetchFn1: getVesselsRA,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Level of R.A.',
        width: '300px',
        groups: vesselGroups2,
        isSearchBoxVisible: false,
        isSelectAllVisible: false,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    sizeKey: 'md',
    columns: getRiskAssessmentColumns,
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsAllTabVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
    responsive: true,
    responsiveConfig: {
      designName: 'card-list-2',
      component: RiskAssessmentListItem
    },
  },

  [WidgetConstant.DEFICIENCIES]: {
    title: 'Deficiencies',
    fetchFn1: getDeficienciesForTable,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['Defect', 'Technical Follow-up'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'others'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
    },
    sizeKey: 'md',
    columns: getDefiniciencyColumns,
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'pie',
      defaultComponent: 'grid',
    },
  },

  [WidgetConstant.SURVEYS_CERTIFICATES]: {
    title: 'Surveys and Certificates',
    fetchFn1: getSurveysAndCertsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships,
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'Due within 60 Days'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
      barChartMaxRange: 250,
    },
    sizeKey: 'md',
    getColumns: (currentActiveTab: string) => getSurveysAndCertsColumns(currentActiveTab),
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'grid',
    },
  },
};
