import { renderHook, act } from '@testing-library/react';
import { useInfiniteQuery } from '../useInfiniteQuery';

describe('useInfiniteQuery', () => {
  // Mock data
  const mockData = {
    data: [{ id: 1, name: 'Item 1' }],
    pagination: {
      page: 1,
      limit: 10,
      totalItems: 30,
      totalPages: 3,
    },
  };

  const mockNextPageData = {
    data: [{ id: 2, name: 'Item 2' }],
    pagination: {
      page: 2,
      limit: 10,
      totalItems: 30,
      totalPages: 3,
    },
  };

  // Mock fetch function
  const mockFetchFn = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle initial data fetching correctly', async () => {
    mockFetchFn.mockResolvedValueOnce(mockData);

    const { result } = renderHook(() => useInfiniteQuery(mockFetchFn, { page: 1, limit: 10 }));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBe(null);

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(mockFetchFn).toHaveBeenCalledWith({
      page: 1,
      limit: 10,
    });
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data).toEqual(mockData);
    expect(result.current.hasNextPage).toBe(true);
  });

  it('should handle pagination correctly', async () => {
    mockFetchFn.mockResolvedValueOnce(mockData).mockResolvedValueOnce(mockNextPageData);

    const { result } = renderHook(() => useInfiniteQuery(mockFetchFn, { page: 1, limit: 10 }));

    // Wait for initial data
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.data).toEqual(mockData);

    // Fetch next page
    await act(async () => {
      await result.current.fetchNextPage();
    });

    expect(mockFetchFn).toHaveBeenCalledTimes(2);
    expect(mockFetchFn).toHaveBeenLastCalledWith({
      page: 2,
      limit: 10,
    });
    expect(result.current.data?.data).toEqual([...mockData.data, ...mockNextPageData.data]);
    expect(result.current.isFetchingNextPage).toBe(false);
  });

  it('should handle errors correctly', async () => {
    const error = new Error('Failed to fetch');
    mockFetchFn.mockRejectedValueOnce(error);

    const { result } = renderHook(() => useInfiniteQuery(mockFetchFn, { page: 1, limit: 10 }));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.error).toBe(error);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data).toBe(null);
  });

  it('should not fetch next page when already loading', async () => {
    let resolveSecondCall: (value: any) => void;

    mockFetchFn.mockResolvedValueOnce(mockData).mockImplementationOnce(
      () =>
        new Promise((resolve) => {
          resolveSecondCall = resolve;
        }),
    );

    const { result } = renderHook(() => useInfiniteQuery(mockFetchFn, { page: 1, limit: 10 }));

    // Wait for initial data
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.isLoading).toBe(false);

    // Start fetching next page
    act(() => {
      result.current.fetchNextPage();
    });

    expect(result.current.isFetchingNextPage).toBe(true);

    // Try to fetch next page while already fetching
    act(() => {
      result.current.fetchNextPage();
    });

    // Resolve the pending fetch
    await act(async () => {
      resolveSecondCall(mockNextPageData);
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(mockFetchFn).toHaveBeenCalledTimes(2); // Initial call + one next page call
  });

  it('should handle refetch correctly', async () => {
    mockFetchFn.mockResolvedValueOnce(mockData).mockResolvedValueOnce({
      ...mockData,
      data: [{ id: 3, name: 'Updated Item' }],
    });

    const { result } = renderHook(() => useInfiniteQuery(mockFetchFn, { page: 1, limit: 10 }));

    // Wait for initial data
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Refetch data
    await act(async () => {
      result.current.refetch();
    });

    expect(mockFetchFn).toHaveBeenCalledTimes(2);
    expect(result.current.data?.data).toEqual([{ id: 3, name: 'Updated Item' }]);
  });

  it('should update params when initialParams change', async () => {
    mockFetchFn.mockResolvedValue(mockData);

    const { result, rerender } = renderHook((props) => useInfiniteQuery(mockFetchFn, props), {
      initialProps: { page: 1, limit: 10 },
    });

    // Wait for initial data
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Update params
    rerender({ page: 1, limit: 20 });

    // Wait for refetch with new params
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(mockFetchFn).toHaveBeenLastCalledWith({
      page: 1,
      limit: 20,
    });
  });
});
