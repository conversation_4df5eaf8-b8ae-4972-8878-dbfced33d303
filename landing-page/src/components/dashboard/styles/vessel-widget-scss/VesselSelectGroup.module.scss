@use "./variables" as *;

.raSelectWrapper {
  position: relative;
  flex: 1 1 auto;
  width: 300px;
}

.raSelect {
  border: 1px solid #d1d5db;
  color: #6b7280;
  appearance: none;
  border-radius: 0.375rem;
  padding-left: 1rem;
  font-size: 14px;
  width: 100%;
  background-color: white;
  font-weight: 350;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px #143857;
  }
}

.raPyDefault {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

@for $i from 1 through 8 {
  .ra-py-#{$i} {
    padding-top: #{$i * 0.25}rem;
    padding-bottom: #{$i * 0.25}rem;
  }
}

.raChevron {
  pointer-events: none;
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.raSmallHeight {
  height: 40px;
}