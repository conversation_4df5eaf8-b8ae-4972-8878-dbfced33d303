import { renderHook, waitFor } from '@testing-library/react';
import { AxiosResponse } from 'axios';
import { useTableauTokens } from '../useTableauToken';
import * as tableauProxy from '../../services/tableau-proxy';

jest.mock('../../services/tableau-proxy');

const createMockAxiosResponse = (token: string): AxiosResponse => ({
  data: { token },
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {} as any,
});

describe('useTableauTokens', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return empty array when hasTableauAccess is false', () => {
    const { result } = renderHook(() => useTableauTokens({ nums: 2, hasTableauAccess: false }));
    expect(result.current).toEqual([null]);
  });

  it('should fetch tableau tokens when hasTableauAccess is true', async () => {
    const mockToken = '4Smvua68S2idkWLXKafk0A==:amMZb-pZPGm-zc3eTTRepNSn';
    jest
      .spyOn(tableauProxy, 'getTableauToken')
      .mockResolvedValue(createMockAxiosResponse(mockToken));
    jest.spyOn(tableauProxy, 'generateTableauCredentials');

    const { result } = renderHook(() => useTableauTokens({ nums: 2, hasTableauAccess: true }));

    await waitFor(() => {
      expect(result.current).toEqual([mockToken, mockToken]);
    });

    expect(tableauProxy.generateTableauCredentials).toHaveBeenCalledTimes(2);
    expect(tableauProxy.getTableauToken).toHaveBeenCalledTimes(2);
  });

  it('should return error state when tableau authentication fails', async () => {
    jest
      .spyOn(tableauProxy, 'getTableauToken')
      .mockRejectedValue(new Error('Authentication failed'));
    jest.spyOn(tableauProxy, 'generateTableauCredentials');

    const { result } = renderHook(() => useTableauTokens({ nums: 2, hasTableauAccess: true }));

    await waitFor(() => {
      expect(result.current).toEqual(['error', 'error']);
    });

    expect(tableauProxy.generateTableauCredentials).toHaveBeenCalledTimes(2);
    expect(tableauProxy.getTableauToken).toHaveBeenCalledTimes(2);
  });

  it('should fetch correct number of tokens based on nums prop', async () => {
    const mockToken = '4Smvua68S2idkWLXKafk0A==:amMZb-pZPGm-zc3eTTRepNSn';
    jest
      .spyOn(tableauProxy, 'getTableauToken')
      .mockResolvedValue(createMockAxiosResponse(mockToken));
    jest.spyOn(tableauProxy, 'generateTableauCredentials');

    const { result } = renderHook(() => useTableauTokens({ nums: 1, hasTableauAccess: true }));

    await waitFor(() => {
      expect(result.current).toEqual([mockToken]);
    });

    expect(tableauProxy.generateTableauCredentials).toHaveBeenCalledTimes(1);
    expect(tableauProxy.getTableauToken).toHaveBeenCalledTimes(1);
  });

  it('should update tokens when nums prop changes', async () => {
    const mockToken = '4Smvua68S2idkWLXKafk0A==:amMZb-pZPGm-zc3eTTRepNSn';
    jest
      .spyOn(tableauProxy, 'getTableauToken')
      .mockResolvedValue(createMockAxiosResponse(mockToken));

    const { result, rerender } = renderHook(
      ({ nums, hasTableauAccess }) => useTableauTokens({ nums, hasTableauAccess }),
      { initialProps: { nums: 1, hasTableauAccess: true } },
    );

    await waitFor(() => {
      expect(result.current).toEqual([mockToken]);
    });

    rerender({ nums: 2, hasTableauAccess: true });

    await waitFor(() => {
      expect(result.current).toEqual([mockToken, mockToken]);
    });
  });
});
