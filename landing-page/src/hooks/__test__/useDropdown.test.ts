import { act, renderHook } from '@testing-library/react';
import { useDropdown } from '../useDropdown';
describe('useDropdown', () => {
  it('should initialize with isOpen as false', () => {
    const { result } = renderHook(() => useDropdown());
    expect(result.current.isOpen).toBe(false);
  });

  it('should toggle isOpen state when toggleDropdown is called', () => {
    const { result } = renderHook(() => useDropdown());

    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(true);

    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(false);
  });

  it('should close dropdown when clicking outside', () => {
    const { result } = renderHook(() => useDropdown());
    const div = document.createElement('div');
    document.body.appendChild(div);

    // Mock the ref current value
    result.current.dropdownRef.current = div;

    // Open the dropdown
    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(true);

    // Simulate click outside
    act(() => {
      const clickEvent = new MouseEvent('mousedown', { bubbles: true });
      document.dispatchEvent(clickEvent);
    });
    expect(result.current.isOpen).toBe(false);

    document.body.removeChild(div);
  });

  it('should not close dropdown when clicking inside', () => {
    const { result } = renderHook(() => useDropdown());
    const div = document.createElement('div');
    const innerElement = document.createElement('button');
    div.appendChild(innerElement);
    document.body.appendChild(div);

    // Mock the ref current value
    result.current.dropdownRef.current = div;

    // Open the dropdown
    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(true);

    // Simulate click inside
    act(() => {
      const clickEvent = new MouseEvent('mousedown', { bubbles: true });
      innerElement.dispatchEvent(clickEvent);
    });
    expect(result.current.isOpen).toBe(true);

    document.body.removeChild(div);
  });

  it('should clean up event listener on unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
    const { unmount } = renderHook(() => useDropdown());

    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));

    removeEventListenerSpy.mockRestore();
  });
});
