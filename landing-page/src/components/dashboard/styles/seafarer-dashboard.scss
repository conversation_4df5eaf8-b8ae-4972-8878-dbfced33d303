.seafarer-landing-page-container {
  margin-top: 70px;
  padding-top: 20px;
}

.seafarer-greeting {
  font: normal normal bold 24px/30px Inter, sans-serif;
}

.world-clock-card,
.tolas-card,
.news-card {
  margin-bottom: 0.5rem;
  border: 1px solid #efefef;
  border-radius: 10;
  box-shadow: 0 0 2.39px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}

.tolas-text {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 16px;
}

.tolas-button {
  padding: 5px 20px;
}

.news-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  margin-bottom: 10px;
  border-radius: 5px;
}

.news-image-small {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 5px;
}

.news-category {
  font: 14px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 5px;
}

.news-date {
  font: 14px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 5px;
}

.news-title {
  font-size: 24px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10px;
}

.news-description {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-bottom: 10px;
}

.read-more {
  text-decoration: underline;
  color: #0091b8;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 10px;
}

.news-card-small {
  margin-bottom: 1rem;
  cursor: pointer;
}

.see-more-container {
  padding: 0px 20px;
}

.see-more-button {
  text-decoration: underline;
  color: #0091b8;
  cursor: pointer;
  font-size: 14px;
}

.tolas-title {
  font-size: 16px;
  font-weight: bold;
}

.tolas-header {
  display: flex;
  gap: 10px;
  place-items: center;
  margin-bottom: 20px;
}

.fleet-news-title {
  font-size: 16px;
  font-weight: bold;
  color: #1f4a70;
}

.card-news-category,
.card-news-date {
  font-size: 12px;
  font-weight: 400;
  color: #6c757d;
}

.card-news-title {
  font-size: 13px;
  font-weight: 600;
  color: #333333;
  margin: 5px 0px 5px 0px;
}

.news-card-small .card-body {
  padding: 10px;
}

.card-news-content {
  margin-top: 5px;
  margin-left: -10px;
}

.border-bottom {
  border-bottom: 1px solid #efefef;
  margin: 20px 0 30px 0;
}

.news-shimmer {
  .shimmer-box {
    background: #f6f7f8;
    background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    animation: shimmer 1.5s linear infinite;
    border-radius: 5px;
  }

  .shimmer-text {
    background: #f6f7f8;
    background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    animation: shimmer 1.5s linear infinite;
    border-radius: 3px;

    &.short {
      width: 100px;
      height: 14px;
      margin-bottom: 5px;
    }

    &.medium {
      width: 200px;
      height: 20px;
      margin-bottom: 10px;
    }

    &.long {
      width: 100%;
      height: 40px;
      margin-bottom: 10px;
    }
  }

  .major-news .shimmer-box {
    width: 100%;
    height: 200px;
    margin-bottom: 10px;
  }

  .news-image-small.shimmer-box {
    width: 120px;
    height: 80px;
  }

  @keyframes shimmer {
    0% {
      background-position: -468px 0;
    }

    100% {
      background-position: 468px 0;
    }
  }
}
