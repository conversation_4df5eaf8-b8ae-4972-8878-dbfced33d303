import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ModuleModal } from '../dashboard/VesselWidget/ModuleModal';

describe('ModuleModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    sizeKey: 'md' as const,
    children: <div>Modal Content</div>,
  };

  const mockShowModal = jest.fn();
  const mockClose = jest.fn();

  beforeAll(() => {
    // Define all properties we want to mock
    Object.defineProperties(window.HTMLDialogElement.prototype, {
      showModal: {
        configurable: true,
        value: mockShowModal,
      },
      close: {
        configurable: true,
        value: mockClose,
      }
    });
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render when isOpen is true', () => {
    const { container } = render(<ModuleModal {...defaultProps} />);
    
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
    expect(container.querySelector('dialog button')).toHaveAttribute('aria-label', 'Close modal');
    expect(mockShowModal).toHaveBeenCalled();
  });

  it('should not render when isOpen is false', () => {
    const { container } = render(<ModuleModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
    expect(container.querySelector('dialog')).not.toBeInTheDocument();
  });

  it('should call onClose when overlay is clicked', () => {
    const onClose = jest.fn();
    const { container } = render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const overlay = container.querySelector('dialog button')!;
    fireEvent.click(overlay);
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('should not call onClose when modal content is clicked', () => {
    const onClose = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const modalContent = screen.getByText('Modal Content');
    fireEvent.click(modalContent);
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('should render children correctly', () => {
    const children = (
      <div>
        <h1>Test Title</h1>
        <p>Test paragraph</p>
        <button>Test Button</button>
      </div>
    );
    
    render(<ModuleModal {...defaultProps}>{children}</ModuleModal>);
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test paragraph')).toBeInTheDocument();
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  it('should handle different size keys', () => {
    const { container, unmount } = render(<ModuleModal {...defaultProps} sizeKey="sm" />);
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
    expect(container.querySelector('dialog')).toBeInTheDocument();
    unmount();

    render(<ModuleModal {...defaultProps} sizeKey="lg" />);
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('should stop propagation on modal content click', () => {
    const onClose = jest.fn();
    const { container } = render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const dialog = container.querySelector('dialog')!;
    fireEvent.click(dialog);
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('should handle keyboard accessibility', () => {
    const { container } = render(<ModuleModal {...defaultProps} />);
    
    const closeButton = container.querySelector('dialog button')!;
    expect(closeButton).toHaveAttribute('type', 'button');
    expect(closeButton).toHaveAttribute('aria-label', 'Close modal');
  });

  it('should render with complex children', () => {
    const complexChildren = (
      <div>
        <header>Header</header>
        <main>
          <section>Section 1</section>
          <section>Section 2</section>
        </main>
        <footer>Footer</footer>
      </div>
    );
    
    render(<ModuleModal {...defaultProps}>{complexChildren}</ModuleModal>);
    
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Section 1')).toBeInTheDocument();
    expect(screen.getByText('Section 2')).toBeInTheDocument();
    expect(screen.getByText('Footer')).toBeInTheDocument();
  });

  it('should handle multiple modal instances', () => {
    const onClose1 = jest.fn();
    const onClose2 = jest.fn();
    
    const { container } = render(
      <div>
        <ModuleModal
          isOpen={true}
          onClose={onClose1}
          sizeKey="sm"
        >
          <div>Modal 1</div>
        </ModuleModal>
        <ModuleModal
          isOpen={true}
          onClose={onClose2}
          sizeKey="lg"
        >
          <div>Modal 2</div>
        </ModuleModal>
      </div>
    );
    
    expect(screen.getByText('Modal 1')).toBeInTheDocument();
    expect(screen.getByText('Modal 2')).toBeInTheDocument();
    
    const dialogs = container.querySelectorAll('dialog');
    expect(dialogs).toHaveLength(2);
    
    const closeButtons = container.querySelectorAll('dialog button');
    expect(closeButtons).toHaveLength(2);
  });
});
