import React from 'react';
import { render, screen } from '@testing-library/react';
import AlertGuide from '../dashboard/AlertGuide';
import { Icon } from '../../StyleGuide';

// Mock the StyleGuide Icon component
jest.mock('../../StyleGuide', () => ({
  Icon: jest.fn(() => <div data-testid="mock-icon" />)
}));

describe('AlertGuide Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Default Mode', () => {
    it('should render with default text when no props are provided', () => {
      render(<AlertGuide />);
      expect(screen.getByText('Coming soon')).toBeInTheDocument();
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
      expect(document.querySelector('.report-widget-content')).toBeInTheDocument();
    });

    it('should render with custom text when text prop is provided', () => {
      const customText = 'Custom Message';
      render(<AlertGuide text={customText} />);
      expect(screen.getByText(customText)).toBeInTheDocument();
    });

    it('should render empty text when text prop is null', () => {
      render(<AlertGuide text={null} />);
      const textElement = document.querySelector('.coming-soon-text');
      expect(textElement).toBeInTheDocument();
      expect(textElement).toHaveTextContent('');
    });

    it('should render default icon with correct props', () => {
      render(<AlertGuide />);
      expect(Icon).toHaveBeenCalledWith(
        expect.objectContaining({
          icon: 'timetable',
          size: 26,
          className: 'text-primary'
        }),
        expect.any(Object)
      );
    });
  });

  describe('Custom Mode (with icon and caption)', () => {
    it('should render custom icon and caption when both are provided', () => {
      const customCaption = 'Custom Caption';
      const customIcon = <div data-testid="custom-icon">Custom Icon</div>;
      
      render(<AlertGuide caption={customCaption} icon={customIcon} />);
      
      expect(screen.getByText(customCaption)).toBeInTheDocument();
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
      expect(document.querySelector('.alert-guide-wrapper')).toBeInTheDocument();
      expect(document.querySelector('.alert-guide-text')).toBeInTheDocument();
    });

    it('should not enter custom mode when only caption is provided', () => {
      render(<AlertGuide caption="Only Caption" />);
      expect(screen.getByText('Coming soon')).toBeInTheDocument();
    });

    it('should not enter custom mode when only icon is provided', () => {
      const customIcon = <div data-testid="custom-icon">Custom Icon</div>;
      render(<AlertGuide icon={customIcon} />);
      expect(screen.getByText('Coming soon')).toBeInTheDocument();
    });
  });

  describe('CSS Classes', () => {
    it('should apply correct CSS classes in default mode', () => {
      render(<AlertGuide />);
      expect(document.querySelector('.report-widget-content')).toBeInTheDocument();
      expect(document.querySelector('.coming-soon-text')).toBeInTheDocument();
    });

    it('should apply correct CSS classes in custom mode', () => {
      const customIcon = <div>Custom Icon</div>;
      render(<AlertGuide caption="Custom Caption" icon={customIcon} />);
      expect(document.querySelector('.alert-guide-wrapper')).toBeInTheDocument();
      expect(document.querySelector('.alert-guide-text')).toBeInTheDocument();
    });
  });
});