import { act, renderHook } from '@testing-library/react';
import { useInfiniteScroll } from '../useInfiniteScroll';

describe('useInfiniteScroll', () => {
  const mockFetchNextPage = jest.fn();
  const baseProps = {
    fetchNextPage: mockFetchNextPage,
    isFetchingNextPage: false,
    hasNextPage: true,
    dataLength: 10,
  };

  beforeEach(() => {
    mockFetchNextPage.mockClear();
  });

  it('should return containerRef and handleScroll function', () => {
    const { result } = renderHook(() => useInfiniteScroll(baseProps));
    expect(result.current.containerRef).toBeDefined();
    expect(result.current.handleScroll).toBeInstanceOf(Function);
  });

  describe('handleScroll', () => {
    it('should not fetch when there is no next page', () => {
      const { result } = renderHook(() => useInfiniteScroll({ ...baseProps, hasNextPage: false }));

      act(() => {
        result.current.handleScroll();
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should not fetch when already fetching', () => {
      const { result } = renderHook(() =>
        useInfiniteScroll({ ...baseProps, isFetchingNextPage: true }),
      );

      act(() => {
        result.current.handleScroll();
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should not fetch when container ref is null', () => {
      const { result } = renderHook(() => useInfiniteScroll(baseProps));

      act(() => {
        result.current.handleScroll();
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should fetch when scrolled near bottom', () => {
      const { result } = renderHook(() => useInfiniteScroll(baseProps));
      const div = document.createElement('div');

      // Mock scroll properties to trigger fetch (within threshold)
      Object.defineProperties(div, {
        scrollTop: { value: 500, writable: true },
        scrollHeight: { value: 1000, writable: true },
        clientHeight: { value: 400, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        result.current.handleScroll();
      });
      expect(mockFetchNextPage).toHaveBeenCalled();
    });

    it('should not fetch when not scrolled near bottom', () => {
      const { result } = renderHook(() => useInfiniteScroll(baseProps));
      const div = document.createElement('div');

      // Mock scroll properties (not within threshold)
      Object.defineProperties(div, {
        scrollTop: { value: 200, writable: true },
        scrollHeight: { value: 1000, writable: true },
        clientHeight: { value: 400, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        result.current.handleScroll();
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });
  });

  describe('initial content check', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should fetch more if content is shorter than container', () => {
      const { result } = renderHook(() => useInfiniteScroll(baseProps));
      const div = document.createElement('div');

      // Mock container properties (content is shorter than container)
      Object.defineProperties(div, {
        scrollHeight: { value: 400, writable: true },
        clientHeight: { value: 500, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        jest.advanceTimersByTime(200); // Advance past the 150ms timeout
      });
      expect(mockFetchNextPage).toHaveBeenCalled();
    });

    it('should not fetch if content is taller than container', () => {
      const { result } = renderHook(() => useInfiniteScroll(baseProps));
      const div = document.createElement('div');

      // Mock container properties (content is taller than container)
      Object.defineProperties(div, {
        scrollHeight: { value: 600, writable: true },
        clientHeight: { value: 500, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        jest.advanceTimersByTime(200);
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should not fetch if already fetching', () => {
      const { result } = renderHook(() =>
        useInfiniteScroll({ ...baseProps, isFetchingNextPage: true }),
      );
      const div = document.createElement('div');

      // Mock container properties (content is shorter than container)
      Object.defineProperties(div, {
        scrollHeight: { value: 400, writable: true },
        clientHeight: { value: 500, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        jest.advanceTimersByTime(200);
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should not fetch if no next page', () => {
      const { result } = renderHook(() => useInfiniteScroll({ ...baseProps, hasNextPage: false }));
      const div = document.createElement('div');

      // Mock container properties (content is shorter than container)
      Object.defineProperties(div, {
        scrollHeight: { value: 400, writable: true },
        clientHeight: { value: 500, writable: true },
      });

      result.current.containerRef.current = div;

      act(() => {
        jest.advanceTimersByTime(200);
      });
      expect(mockFetchNextPage).not.toHaveBeenCalled();
    });

    it('should clean up timer on unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      const { unmount } = renderHook(() => useInfiniteScroll(baseProps));

      unmount();
      expect(clearTimeoutSpy).toHaveBeenCalled();

      clearTimeoutSpy.mockRestore();
    });
  });

  it('should update dependencies when props change', () => {
    const { rerender } = renderHook((props) => useInfiniteScroll(props), {
      initialProps: baseProps,
    });

    const newProps = {
      ...baseProps,
      hasNextPage: false,
      isFetchingNextPage: true,
    };

    rerender(newProps);

    // No explicit assertion needed - if the hook doesn't handle prop changes properly,
    // other tests would fail when testing the scroll behavior
  });
});
