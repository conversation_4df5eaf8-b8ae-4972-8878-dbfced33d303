import React, { useRef, useEffect } from 'react';
import classNames from 'classnames';
import styles from '../styles/vessel-widget-scss/VesselModule.module.scss';

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  sizeKey: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export const ModuleModal: React.FC<ModuleModalProps> = ({ isOpen, onClose, sizeKey, children }) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  // Open effect
  useEffect(() => {
    if (isOpen) {
      dialogRef.current?.showModal();
    }
  }, [isOpen]);

  // Close effect
  useEffect(() => {
    if (!isOpen) {
      dialogRef.current?.close();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <dialog
      ref={dialogRef}
      className={styles['ra-modal-dialog']}
      onCancel={onClose}
      aria-modal="true"
    >
      {/* Overlay */}
      <button
        type="button"
        className={styles['ra-modal-overlay']}
        onClick={(e) => {
          if (e.target === e.currentTarget) onClose();
        }}
        aria-label="Close modal"
      />

      {/* Content */}
      <div
        className={classNames(styles['ra-modal-content'], styles[`size-${sizeKey}`])}
        tabIndex={-1}
      >
        {children}
      </div>
    </dialog>
  );
};
