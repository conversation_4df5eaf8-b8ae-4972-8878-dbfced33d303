import React from 'react';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { SeafarerDashboard } from '../../components/dashboard/SeafarerDashboard';
import { GlobalContext } from '../../context/dashboard-context';
import * as rssService from '../../services/rss-service';
import * as spService from '../../services/sharepoint-service';

jest.mock('../../StyleGuide.tsx');

process.env.LMS_URL = 'https://lms.example.com';

const keycloak = {
  tokenParsed: {
    email: '<EMAIL>',
    name: 'Test Seafarer',
    user_id: 'test.seafarer',
    is_user_onboarded: true,
  },
  realmAccess: {
    roles: [],
  },
};

const mockGa4EventTrigger = jest.fn();

const renderComponent = (props = {}) => {
  return render(
    <GlobalContext.Provider value={{ ga4EventTrigger: mockGa4EventTrigger }}>
      <SeafarerDashboard keycloak={keycloak} {...props} />
    </GlobalContext.Provider>,
  );
};

describe('SeafarerDashboard', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render seafarer greeting with user name', async () => {
    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      expect(screen.getByText(/Hello,.*Test Seafarer!/)).toBeInTheDocument();
    });
  });

  test('should render TOLAS card with start training button', async () => {
    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      const tolasElements = screen.getAllByText(/TOLAS/);
      expect(tolasElements).toHaveLength(2);
      expect(screen.getByText('Start Training')).toBeInTheDocument();
    });
  });

  test('should render fleet news section with major news and other news items', async () => {
    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      expect(screen.getByText('Fleet News')).toBeInTheDocument();
      expect(screen.getByText('Major Fleet Update: New Safety Protocols')).toBeInTheDocument();
      expect(screen.getByText('Crew Training Update')).toBeInTheDocument();
      expect(screen.getAllByText('News')).toHaveLength(3);
      expect(screen.getAllByText('Insights')).toHaveLength(2);
    });
  });

  test('should trigger ga4 event on news item click', async () => {
    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      const newsItem = screen.getByText('Crew Training Update').closest('.news-card-small');
      fireEvent.click(newsItem!);
      expect(mockGa4EventTrigger).toHaveBeenCalledWith('News Click', 'News_2_Insights');
    });
  });

  test('should trigger ga4 event and open news page on see more click', async () => {
    const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);

    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      const seeMoreButton = screen.getByText('See More');
      fireEvent.click(seeMoreButton);
      expect(mockGa4EventTrigger).toHaveBeenCalledWith('News', 'See More News');
      expect(windowOpenSpy).toHaveBeenCalledWith('/news', '_blank');
    });

    windowOpenSpy.mockRestore();
  });

  test('should render fallback name if keycloak name is undefined', async () => {
    const keycloakNoName = {
      ...keycloak,
      tokenParsed: {
        ...keycloak.tokenParsed,
        name: undefined,
      },
    };

    act(() => {
      renderComponent({ keycloak: keycloakNoName });
    });

    await waitFor(() => {
      expect(screen.getByText(/Seafarer!/)).toBeInTheDocument();
    });
  });
});
