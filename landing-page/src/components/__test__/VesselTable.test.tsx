import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VesselTable from '../dashboard/VesselWidget/VesselTable';
import { Vessel } from '../../types/types';
import { EMPTY_STATE_MESSAGES } from 'src/utils/textConstants';

// Mock the hooks and components
jest.mock('../../hooks/useInfiniteScroll', () => ({
  useInfiniteScroll: jest.fn(),
}));

jest.mock('../dashboard/VesselWidget/Spinner', () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

describe('VesselTable', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: [{ status: 'active', flag: 'US' }],
      type: 'cargo',
      vessel_ownership_id: 101,
      risk_id: 1,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: [{ status: 'inactive', flag: 'UK' }],
      type: 'tanker',
      vessel_ownership_id: 102,
      risk_id: 2,
      vessel_id: 2,
    },
  ];

  const defaultProps = {
    vessels: mockVessels,
    tableHeaders: ['Name', 'Status', 'Type'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    onSendEmail: jest.fn(),
    onVesselClick: jest.fn(),
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
    pagination: {
      totalItems: 10,
      totalPages: 2,
      page: 1,
      pageSize: 5,
    },
    cellStyleType: 'default' as const,
    sortConfig: null,
    onSort: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Set default mock implementation for useInfiniteScroll
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;
    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: jest.fn(),
    });
  });

  it('should render loading state when isLoading is true', () => {
    render(<VesselTable {...defaultProps} isLoading={true} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render no results message when vessels array is empty', () => {
    render(<VesselTable {...defaultProps} vessels={[]} isLoading={false} />);

    expect(screen.getByText(EMPTY_STATE_MESSAGES.NO_PENDING_RISK_ASSESSMENTS)).toBeInTheDocument();
  });

  it('should render table headers correctly', () => {
    render(<VesselTable {...defaultProps} />);
  });

  it('should render vessel rows correctly', () => {
    render(<VesselTable {...defaultProps} />);

    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Vessel Beta')).toBeInTheDocument();
  });

  it('should call onVesselClick when vessel name is clicked', () => {
    const onVesselClick = jest.fn();
    render(<VesselTable {...defaultProps} onVesselClick={onVesselClick} />);

    const vesselButton = screen.getByText('Vessel Alpha');
    fireEvent.click(vesselButton);

    expect(onVesselClick).toHaveBeenCalledWith(mockVessels[0]);
  });

  it('should call onSort when header is clicked', () => {
    const onSort = jest.fn();
    render(<VesselTable {...defaultProps} onSort={onSort} />);

    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);

    expect(onSort).toHaveBeenCalledWith('Name');
  });

  it('should show loading indicator when fetching next page', () => {
    render(<VesselTable {...defaultProps} isFetchingNextPage={true} />);

    const loadingIndicators = screen.getAllByTestId('spinner');
    expect(loadingIndicators).toHaveLength(1); // One for the loading indicator
  });

  it('should handle pagination correctly', () => {
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;
    const mockFetchNextPage = jest.fn();

    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: jest.fn(),
    });

    render(
      <VesselTable
        {...defaultProps}
        fetchNextPage={mockFetchNextPage}
        pagination={{
          totalItems: 20,
          totalPages: 4,
          page: 2,
          pageSize: 5,
        }}
      />,
    );

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      hasNextPage: true, // page 2 < totalPages 4
      dataLength: 2, // vessels.length
    });
  });

  it('should handle no pagination', () => {
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;

    render(<VesselTable {...defaultProps} pagination={undefined} />);

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // no pagination means no next page
      dataLength: 2,
    });
  });

  it('should handle conditional cell styling', () => {
    render(<VesselTable {...defaultProps} cellStyleType="conditional" />);

    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Vessel Beta')).toBeInTheDocument();
  });

  it('should handle undefined vessels array', () => {
    render(<VesselTable {...defaultProps} vessels={undefined as any} />);

    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle scroll events', () => {
    const mockHandleScroll = jest.fn();
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;

    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: mockHandleScroll,
    });

    const { container } = render(<VesselTable {...defaultProps} />);

    // Find the div that contains the table (should be the first div)
    const tableContainer = container.querySelector('div');
    expect(tableContainer).toBeInTheDocument();

    // Verify that useInfiniteScroll was called (which means scroll handling is set up)
    expect(useInfiniteScroll).toHaveBeenCalled();
  });

  it('should handle vessels with missing properties', () => {
    const incompleteVessels: Vessel[] = [
      {
        name: 'Incomplete Vessel',
        vesselData: [],
        type: 'unknown',
      } as Vessel,
    ];

    render(<VesselTable {...defaultProps} vessels={incompleteVessels} />);

    expect(screen.getByText('Incomplete Vessel')).toBeInTheDocument();
  });

  it('should handle sort configuration', () => {
    const sortConfig = {
      key: 'Name',
      direction: 'ascending' as const,
    };

    render(<VesselTable {...defaultProps} sortConfig={sortConfig} />);

    expect(screen.getByText('Name')).toBeInTheDocument();
  });

  it('should handle last page correctly', () => {
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;

    render(
      <VesselTable
        {...defaultProps}
        pagination={{
          totalItems: 10,
          totalPages: 2,
          page: 2, // Last page
          pageSize: 5,
        }}
      />,
    );

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // page 2 === totalPages 2
      dataLength: 2,
    });
  });

  it('should render table structure correctly', () => {
    const { container } = render(<VesselTable {...defaultProps} />);

    const table = container.querySelector('table');
    expect(table).toBeInTheDocument();

    const thead = container.querySelector('thead');
    expect(thead).toBeInTheDocument();

    const tbody = container.querySelector('tbody');
    expect(tbody).toBeInTheDocument();
  });

  it('should handle multiple vessel clicks', () => {
    const onVesselClick = jest.fn();
    render(<VesselTable {...defaultProps} onVesselClick={onVesselClick} />);

    const vesselAlpha = screen.getByText('Vessel Alpha');
    const vesselBeta = screen.getByText('Vessel Beta');

    fireEvent.click(vesselAlpha);
    fireEvent.click(vesselBeta);

    expect(onVesselClick).toHaveBeenCalledTimes(2);
    expect(onVesselClick).toHaveBeenNthCalledWith(1, mockVessels[0]);
    expect(onVesselClick).toHaveBeenNthCalledWith(2, mockVessels[1]);
  });

  it('should handle complex pagination scenarios', () => {
    const scenarios = [
      { page: 1, totalPages: 1, expectedHasNext: false },
      { page: 1, totalPages: 5, expectedHasNext: true },
      { page: 3, totalPages: 3, expectedHasNext: false },
      { page: 2, totalPages: 10, expectedHasNext: true },
    ];

    scenarios.forEach(({ page, totalPages, expectedHasNext }) => {
      const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;

      const { unmount } = render(
        <VesselTable
          {...defaultProps}
          pagination={{
            totalItems: totalPages * 5,
            totalPages,
            page,
            pageSize: 5,
          }}
        />,
      );

      expect(useInfiniteScroll).toHaveBeenCalledWith({
        fetchNextPage: expect.any(Function),
        isFetchingNextPage: false,
        hasNextPage: expectedHasNext,
        dataLength: 2,
      });

      unmount();
    });
  });

  it('should not truncate a short vessel name in conditional styling', () => {
    const shortName = 'Vessel A';
    const vesselsWithShortName = [{ ...mockVessels[0], name: shortName }];
    render(<VesselTable {...defaultProps} vessels={vesselsWithShortName} cellStyleType="conditional" />);
    expect(screen.getByText(shortName)).toBeInTheDocument();
    expect(screen.getByText(shortName)).not.toHaveAttribute('title');
  });

  it('should render the table when vessels array is not empty', () => {
    render(<VesselTable {...defaultProps} />);
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.queryByTestId('empty-state')).not.toBeInTheDocument();
  });
  
  it('should render "No results found" when vessels is null', () => {
    render(<VesselTable {...defaultProps} vessels={null as any} />);
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  // Test cases for Spinner visibility
  it('should render the spinner when isFetchingNextPage is true', () => {
    render(<VesselTable {...defaultProps} isFetchingNextPage={true} />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('should not render the spinner when isFetchingNextPage is false', () => {
    render(<VesselTable {...defaultProps} isFetchingNextPage={false} />);
    expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
  });

  // Test case for complex vesselData
  it('should render `vesselData` with more than 2 elements', () => {
    const complexVessel = [{ ...mockVessels[0], vesselData: ['Task A', 'Critical', 'Extra Data'] }];
    const complexHeaders = ['Name', 'Task', 'RA Level', 'Extra'];
    render(<VesselTable {...defaultProps} vessels={complexVessel} tableHeaders={complexHeaders} />);
    expect(screen.getByText('Extra Data')).toBeInTheDocument();
  });

  // Test case for useInfiniteScroll hook
  it('should call useInfiniteScroll with correct props', () => {
    const mockFetchNextPage = jest.fn();
    render(<VesselTable {...defaultProps} fetchNextPage={mockFetchNextPage} />);
    const useInfiniteScroll = require('../../hooks/useInfiniteScroll').useInfiniteScroll;
    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      hasNextPage: true,
      dataLength: 2,
    });
  });


  it('should call onVesselClick with correct vessel object', () => {
    const onVesselClick = jest.fn();
    render(<VesselTable {...defaultProps} onVesselClick={onVesselClick} />);
    const vesselButton = screen.getByRole('button', { name: 'Vessel Alpha' });
    fireEvent.click(vesselButton);
    expect(onVesselClick).toHaveBeenCalledWith(mockVessels[0]);
  });

  describe('Badge Components', () => {
    it('should render StatusBadge with conditional cell style', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['truncated', 'Critical', 'Approved'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status', 'Approval']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Critical')).toBeInTheDocument();
      expect(screen.getByText('Approved')).toBeInTheDocument();
    });

    it('should handle different status values with conditional styling', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data', 'Special', 'Pending'],
        },
        {
          ...mockVessels[1],
          vesselData: ['data', 'Unassigned', 'Rejected'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status', 'Approval']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Special')).toBeInTheDocument();
      expect(screen.getByText('Unassigned')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
      expect(screen.getByText('Rejected')).toBeInTheDocument();
    });

    it('should handle unknown status values with conditional styling', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data', 'Unknown', 'Unknown Status'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status', 'Approval']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Unknown')).toBeInTheDocument();
      expect(screen.getByText('Unknown Status')).toBeInTheDocument();
    });

    it('should handle Approved with Condition status', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data', 'Critical', 'Approved with Condition'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status', 'Approval']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Approved with Condition')).toBeInTheDocument();
    });
  });

  describe('Utility Functions', () => {
    it('should handle invalid hex colors', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data', 'Critical'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status']}
          badgeColors={['invalid-color']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Critical')).toBeInTheDocument();
    });

    it('should handle empty badge colors array', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data', 'Critical'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data', 'Status']}
          badgeColors={[]}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Critical')).toBeInTheDocument();
    });

    it('should handle default badge styling with yellow colors', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['test data'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data']}
          badgeColors={['#fbc02d']}
          cellStyleType="default"
        />,
      );

      expect(screen.getByText('test data')).toBeInTheDocument();
    });

    it('should handle default badge styling with other colors', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['test data'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Data']}
          badgeColors={['#ff0000']}
          cellStyleType="default"
        />,
      );

      expect(screen.getByText('test data')).toBeInTheDocument();
    });
  });

  describe('Cell Style Types', () => {
    it('should handle conditional cell style type', () => {
      render(<VesselTable {...defaultProps} cellStyleType="conditional" />);

      expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    });

    it('should handle default cell style type', () => {
      render(<VesselTable {...defaultProps} cellStyleType="default" />);

      expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    });

    it('should handle conditional cell style with extra columns (default case)', () => {
      const vessels = [
        {
          ...mockVessels[0],
          vesselData: ['data1', 'data2', 'data3', 'data4', 'data5'],
        },
      ];

      render(
        <VesselTable
          {...defaultProps}
          vessels={vessels}
          tableHeaders={['Name', 'Col1', 'Col2', 'Col3', 'Col4', 'Col5']}
          cellStyleType="conditional"
        />,
      );

      expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    });
  });

  describe('Email Button', () => {
    it('should call onSendEmail when email button is clicked', () => {
      const onSendEmail = jest.fn();

      render(<VesselTable {...defaultProps} onSendEmail={onSendEmail} />);

      // Find all buttons and look for the email button by its SVG content
      const buttons = screen.getAllByRole('button');
      const emailButton = buttons.find((button) => {
        const svg = button.querySelector('svg');
        return svg && svg.getAttribute('viewBox') === '0 0 24 24';
      });

      expect(emailButton).toBeTruthy();
      fireEvent.click(emailButton!);
      expect(onSendEmail).toHaveBeenCalledWith(mockVessels[0]);
    });

    it('should call onSendEmail for conditional cell style', () => {
      const onSendEmail = jest.fn();

      render(
        <VesselTable {...defaultProps} onSendEmail={onSendEmail} cellStyleType="conditional" />,
      );

      // Find all buttons and look for the external link button
      const buttons = screen.getAllByRole('button');
      const externalLinkButton = buttons.find((button) => {
        const svg = button.querySelector('svg');
        return svg && svg.getAttribute('viewBox') === '0 0 20 21';
      });

      expect(externalLinkButton).toBeTruthy();
      fireEvent.click(externalLinkButton!);
      expect(onSendEmail).toHaveBeenCalledWith(mockVessels[0]);
    });

    it('should show correct tooltip for conditional cell style', () => {
      render(<VesselTable {...defaultProps} cellStyleType="conditional" />);

      const tooltipElements = screen.getAllByText('View Details');
      expect(tooltipElements.length).toBeGreaterThan(0);
    });

    it('should show correct tooltip for default cell style', () => {
      render(<VesselTable {...defaultProps} cellStyleType="default" />);

      const tooltipElements = screen.getAllByText('Send Email');
      expect(tooltipElements.length).toBeGreaterThan(0);
    });
  });

  describe('Sort Icon Function', () => {
    it('should test getSortIcon function directly', () => {
      const { getSortIcon } = require('../dashboard/VesselWidget/VesselTable');

      // Test with no sort config
      const neutralIcon = getSortIcon('Name', null);
      expect(neutralIcon).toBeDefined();

      // Test with ascending sort
      const ascendingIcon = getSortIcon('Name', { key: 'Name', direction: 'ascending' });
      expect(ascendingIcon).toBeDefined();

      // Test with descending sort
      const descendingIcon = getSortIcon('Name', { key: 'Name', direction: 'descending' });
      expect(descendingIcon).toBeDefined();

      // Test with different key
      const differentKeyIcon = getSortIcon('Status', { key: 'Name', direction: 'ascending' });
      expect(differentKeyIcon).toBeDefined();
    });
  });
});
