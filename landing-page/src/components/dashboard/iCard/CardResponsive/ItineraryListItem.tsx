import React from 'react';
import { format } from 'date-fns';
import { ExternalLinkIcon, RightArrowIcon } from '../../VesselWidget/svgIcons';
import { VesselItinerary } from 'src/context/dashboard-context';
import { handleItineraryClick, handleVesselClick } from 'src/utils/utility';
import { Vessel } from 'src/types/types';

interface VesselCardData extends VesselItinerary {
  last_port: string;
  vessel_ownership_id: number;
}

export default function ItineraryListItem({ data }: Readonly<{ data: VesselCardData }>) {
  const { vessel_name, last_port, port, estimated_arrival, vessel_ownership_id } = data;
  
  return (
    <div className="ra-card-resp">
      <div className="ra-card-content">
        <div className="ra-card-header">
          <button
            onClick={() => {
              handleVesselClick({ vessel_ownership_id } as Vessel)
            }}
            className="card-heading"
          >
            {vessel_name}
          </button>
          <div className="action-button-wrapper">
            <button
              onClick={() => {
                handleItineraryClick(vessel_ownership_id)
              }}
              className="action-button"
            >
              <ExternalLinkIcon height={22} width={22} />
            </button>
          </div>
        </div>
        <div className="ra-detail-wrapper">
          <span>{last_port}</span>
          <RightArrowIcon />
          <span>{port}</span>
        </div>
        <span>ETA: {format(estimated_arrival, 'dd MMM yyyy HH:mm')}</span>
      </div>
    </div>
  );
}
