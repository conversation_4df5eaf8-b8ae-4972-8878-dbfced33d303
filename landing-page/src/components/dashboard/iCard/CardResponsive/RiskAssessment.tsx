import React from 'react';
import { ExternalLinkIcon } from '../../VesselWidget/svgIcons';
import { RiskAssessment } from 'src/types/types';

export default function RiskAssessmentListItem({ data }: Readonly<{ data: RiskAssessment }>) {
  const { id, ra_level, task_requiring_ra, vessel_name, vessel_ownership_id } = data;
  let raLevelClass = '';
  if (ra_level === 'Special') {
    raLevelClass = 'special';
  }
  if (ra_level === 'Critical') {
    raLevelClass = 'critical';
  }
  if (ra_level === 'Unassigned') {
    raLevelClass = 'unassigned';
  }

  return (
    <div className="ra-card-resp">
      <div className="ra-card-content">
        <div className="ra-card-header">
          <button
            onClick={() => {
              const url = `vessel/ownership/details/${vessel_ownership_id}`;
              window.open(url, '_blank');
            }}
            className="card-heading"
          >
            {vessel_name}
          </button>
          <div className="action-button-wrapper">
            <button
              onClick={() => {
                const url = `risk-assessment/approval/${id}`;
                window.open(url, '_blank');
              }}
              className="action-button"
            >
              <ExternalLinkIcon height={22} width={22} />
            </button>
          </div>
        </div>
        <div className="ra-detail-wrapper">
          <span>{task_requiring_ra}</span>
        </div>
        <span className="ra-level">
          <span className={`ra-level-content ${raLevelClass}`}>{ra_level}</span>
        </span>
      </div>
    </div>
  );
}
