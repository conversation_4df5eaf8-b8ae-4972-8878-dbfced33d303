import React from 'react';
import { render, screen, waitFor, act, within } from '@testing-library/react';
import CrewInjuryStatistics from '../dashboard/CrewInjuryStatistics';
import * as vesselService from 'src/services/vessel-service';
import userEvent from '@testing-library/user-event';

// Mock the vessel service
jest.mock('src/services/vessel-service', () => ({
  getCrewInjuryStats: jest.fn()
}));

const mockCrewStats = {
  data: {
    response: [
      {
        INCIDENTDATE: '2024-01-15',
        HANDOVERDATE: '2024-01-01',
        TAKEOVERDATE: '2023-12-01',
        CREWCNT: 20,
        LTI: 1,
        TRC: 2
      },
      {
        INCIDENTDATE: null,
        HANDOVERDATE: '2024-01-01',
        TAKEOVERDATE: '2023-12-01',
        CREWCNT: 18,
        LTI: 0,
        TRC: 0
      }
    ]
  }
};

describe('CrewInjuryStatistics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders fleet crew statistics title', async () => {
    (vesselService.getCrewInjuryStats as jest.Mock).mockResolvedValue(mockCrewStats);
    
    await act(async () => {
      render(<CrewInjuryStatistics />);
    });
    
    expect(screen.getByText('FLEET Crew Statistics')).toBeInTheDocument();
  });

  it('displays tooltips on hover', async () => {
    (vesselService.getCrewInjuryStats as jest.Mock).mockResolvedValue(mockCrewStats);
    
    await act(async () => {
      render(<CrewInjuryStatistics />);
    });
    
    const ltiElement = screen.getByText('LTI Incident(s)');
    await act(async () => {
      userEvent.hover(ltiElement);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Lost Time Injury')).toBeInTheDocument();
    });
  });

  it('handles API error gracefully', async () => {
    const consoleLogSpy = jest.spyOn(console, 'log');
    (vesselService.getCrewInjuryStats as jest.Mock).mockRejectedValue(new Error('API Error'));
    
    await act(async () => {
      render(<CrewInjuryStatistics />);
    });
    
    await waitFor(() => {
      expect(consoleLogSpy).toHaveBeenCalled();
    });
    
    consoleLogSpy.mockRestore();
  });


  it('forwards ref correctly', async () => {
    (vesselService.getCrewInjuryStats as jest.Mock).mockResolvedValue(mockCrewStats);
    
    const ref = React.createRef<any>();
    await act(async () => {
      render(<CrewInjuryStatistics ref={ref} />);
    });

    expect(ref.current).toBeDefined();
    expect(ref.current.crewStatistics).toBeDefined();
  });
});