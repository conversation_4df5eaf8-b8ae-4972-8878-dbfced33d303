import React, { useState, useEffect, useContext } from 'react';
import { StatCard } from '@paris2/styleguide';
import { getOwnerships } from '../../services/vessel-service';
import { getPrecomputedCompliances, VesselCompliance } from '../../services/seafarer-service';
import { VesselOwned } from '../../types/vessel';
import { VESSEL_TYPES } from '../../constants/vessel';
import { GlobalContext } from 'src/context/dashboard-context';

interface KpiCardProps {
  onStatCardDataUpdate?: (cardType: string, data: { count: number; isLoading: boolean }) => void;
  children?: React.ReactNode;
  kpiCards?: Array<{
    id: number;
    name: string;
    description: string | null;
    is_default: boolean;
    default_position: number;
    status: number;
    type: 'W' | 'C';
  }>;
}

export interface KpiCardRef {
  handleStatCardDataUpdate: (cardType: string, data: { count: number; isLoading: boolean }) => void;
}

interface StatCardData {
  surveys: { count: number; isLoading: boolean };
  deficiencies: { count: number; isLoading: boolean };
  riskAssessments: { count: number; isLoading: boolean };
  nonCompliantOcimfTankers: { count: number; isLoading: boolean };
}

const KpiCard = React.forwardRef<KpiCardRef, KpiCardProps>(
  ({ onStatCardDataUpdate, children, kpiCards = [] }, ref) => {
    const { ga4EventTrigger } = useContext(GlobalContext);

    // State to hold StatCard data
    const [statCardData, setStatCardData] = useState<StatCardData>({
      surveys: { count: 0, isLoading: true },
      deficiencies: { count: 0, isLoading: true },
      riskAssessments: { count: 0, isLoading: true },
      nonCompliantOcimfTankers: { count: 0, isLoading: true },
    });

    // State for OCIMF compliance logic (for VM users)
    const [vesselList, setVesselList] = useState<(VesselOwned & { vessel_type?: string })[]>([]);
    const [isVesselLoading, setIsVesselLoading] = useState(false);
    const [ocimfComplianceData, setOcimfComplianceData] = useState<VesselCompliance[]>([]);
    const [isPreComputeApiCalled, setIsPreComputeApiCalled] = useState<boolean>(false);

    // Handler to update StatCard data from CardModuleContainer
    const handleStatCardDataUpdate = (
      cardType: string,
      data: { count: number; isLoading: boolean },
    ) => {
      setStatCardData((prev) => ({
        ...prev,
        [cardType]: data,
      }));

      // Also call the parent callback if provided
      if (onStatCardDataUpdate) {
        onStatCardDataUpdate(cardType, data);
      }
    };

    // Functions for OCIMF compliance logic (for VM users)
    const getMinimalVesselList = async () => {
      try {
        setIsVesselLoading(true);
        const minimalVesselList = await getOwnerships('f=name&f=id&f=vessel.id&f=vessel_type.type');
        const preparedVesselList = minimalVesselList?.data?.results?.map((vessel) => ({
          id: vessel.id,
          name: vessel?.name,
          vessel_id: vessel?.vessel?.id,
          vessel_type: vessel?.vessel_type?.type,
        }));
        setVesselList(preparedVesselList || []);
        setIsVesselLoading(false);
      } catch (e) {
        console.log(e);
        setIsVesselLoading(false);
      }
    };

    const prepareAndSetComplianceData = (complianceData: VesselCompliance[]) => {
      const nonCompliantVesselIds = complianceData?.map((vessel) => vessel.vessel_id);
      const preparedNonCompliantVessels = vesselList.filter((vessel) =>
        nonCompliantVesselIds.includes(vessel.vessel_id),
      );

      // Update the stat card with the count of non-compliant vessels
      const newData = {
        count: preparedNonCompliantVessels.length,
        isLoading: false,
      };

      setStatCardData((prev) => ({
        ...prev,
        nonCompliantOcimfTankers: newData,
      }));

      // Also call the parent callback if provided
      if (onStatCardDataUpdate) {
        onStatCardDataUpdate('nonCompliantOcimfTankers', newData);
      }
    };

    const getOcimfComplianceList = async () => {
      const payload = {
        vessel_ids: vesselList
          ?.filter((vessel) => vessel?.vessel_type === VESSEL_TYPES.TANKER)
          .map((vessel) => vessel.vessel_id),
      };

      try {
        const ocimfComplianceResponse = await getPrecomputedCompliances(payload);
        setOcimfComplianceData(ocimfComplianceResponse.data);
        prepareAndSetComplianceData(ocimfComplianceResponse.data);
        setIsPreComputeApiCalled(true);
      } catch (error) {
        console.error('Error fetching OCIMF compliance data:', error);
        const errorData = {
          count: 0,
          isLoading: false,
        };

        setStatCardData((prev) => ({
          ...prev,
          nonCompliantOcimfTankers: errorData,
        }));

        // Also call the parent callback if provided
        if (onStatCardDataUpdate) {
          onStatCardDataUpdate('nonCompliantOcimfTankers', errorData);
        }
      }
    };

    // Effects for OCIMF compliance logic (only for VM users)
    useEffect(() => {
      getMinimalVesselList();
    }, []);

    useEffect(() => {
      if (vesselList?.length) {
        getOcimfComplianceList();
      }
    }, [vesselList]);

    // Expose the handleStatCardDataUpdate function via ref
    React.useImperativeHandle(ref, () => ({
      handleStatCardDataUpdate,
    }));

    // Function to get card properties based on card name
    const getKpiCardProperties = (cardName: string, ga4EventTrigger: any) => {
      switch (cardName) {
        case 'Surveys & Certificates':
          return {
            name: 'Surveys and Certificate',
            count: statCardData.surveys.count,
            subTitle: 'Expiring within 30 days',
            isLoading: statCardData.surveys.isLoading,
            onClick: undefined,
          };
        case 'Deficiencies':
          return {
            name: 'Deficiencies',
            count: statCardData.deficiencies.count,
            subTitle: 'Overdue',
            isLoading: statCardData.deficiencies.isLoading,
            onClick: () => {
              const url = `/deficiency/list?overdue=true`;
              ga4EventTrigger(
                'KPI Card - Deficiencies',
                `${statCardData.deficiencies.count}`,
                'Click Action Button',
              );
              window.open(url, '_blank');
            },
          };
        case 'Unassigned Risk Assessment':
          return {
            name: 'Unassigned Risk Assessment',
            count: statCardData.riskAssessments.count,
            subTitle: '',
            isLoading: statCardData.riskAssessments.isLoading,
            onClick: () => {
              const url = `/risk-assessment`;
              ga4EventTrigger(
                'KPI Card - RA',
                `${statCardData.riskAssessments.count}`,
                'Click Action Button',
              );
              window.open(url, '_blank');
            },
          };
        case 'Non-Compliant OCIMF Tankers':
          return {
            name: 'Non-Compliant OCIMF Tankers',
            count: statCardData.nonCompliantOcimfTankers.count,
            subTitle: '',
            isLoading: statCardData.nonCompliantOcimfTankers.isLoading,
            onClick: undefined,
          };
        default:
          return {
            count: 0,
            subTitle: '',
            isLoading: true,
            onClick: undefined,
          };
      }
    };

    return (
      <>
        <div className="stat-card-wrapper">
          {kpiCards.length > 0 &&
            // Render kpiCards dynamically based on API response
            kpiCards?.map((card) => {
              const cardProps = getKpiCardProperties(card.name, ga4EventTrigger);
              return (
                <StatCard
                  key={card.id}
                  title={cardProps.name}
                  count={cardProps.count}
                  subTitle={cardProps.subTitle}
                  isLoading={cardProps.isLoading}
                  onClick={cardProps.onClick}
                />
              );
            })}
        </div>
        {children}
      </>
    );
  },
);

export default KpiCard;
