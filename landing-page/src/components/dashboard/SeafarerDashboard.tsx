import React, { useState, useEffect, useContext } from 'react';
import { Card, Col, Container, Row, Button } from 'react-bootstrap';
import { KeycloakProps } from '../../types/keycloak';
import { GlobalContext } from '../../context/dashboard-context';
import { AnalogClock } from '../common';
import { TimezoneDropdownType } from '../../types/widgets';
import '../../pages/styles/dashboard.scss';
import './styles/seafarer-dashboard.scss';
import tolasIcon from '../../assets/images/tolas-icon.svg';
import { getTolasNewsAndInsights } from 'src/services/tolas-service';
import { FLEET_NEWS } from 'src/constants/tolas';

const { LMS_URL, FLEETSHIP_URL } = process.env;

interface NewsItem {
  id: string;
  title: string;
  image: string;
  category: 'News' | 'Insights';
  date: string;
  description: string;
  href: string;
}

const SEAFARER_TIMEZONES: TimezoneDropdownType[] = [
  { city: 'Beijing', timezone: 'Asia/Shanghai' },
  { city: 'Cyprus', timezone: 'Asia/Nicosia' },
  { city: 'Hong Kong', timezone: 'Asia/Hong_Kong' },
  { city: 'Houston', timezone: 'America/Chicago' },
  { city: 'Mumbai', timezone: 'Asia/Kolkata' },
  { city: 'London', timezone: 'Europe/London' },
  { city: 'Oslo', timezone: 'Europe/Oslo' },
  { city: 'Tokyo', timezone: 'Asia/Tokyo' },
];

const SeafarerDashboard: React.FC<KeycloakProps> = ({ keycloak }) => {
  const { ga4EventTrigger } = useContext(GlobalContext);
  const [userZoneInfoList] = useState<TimezoneDropdownType[]>(SEAFARER_TIMEZONES);
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [topNews, setTopNews] = useState<NewsItem | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await getTolasNewsAndInsights();
        if (Array.isArray(response)) {
          setNewsItems(response ?? []);
          setTopNews(response[0] ?? {});
        }
      } catch (err) {
        console.error('Error fetching news:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, []);

  const handleStartTraining = () => {
    if (ga4EventTrigger) ga4EventTrigger('TOLAS Training', 'Start Training Click');
    window.open(LMS_URL, '_blank');
  };

  const handleNewsClick = (newsId: string, category: string, href: string) => {
    if (ga4EventTrigger) ga4EventTrigger('News Click', `News_${newsId}_${category}`);
    window.open(`${FLEETSHIP_URL}${href}`, '_blank');
  };

  const handleSeeMore = () => {
    if (ga4EventTrigger) ga4EventTrigger('News', 'See More News');
    window.open(`${FLEETSHIP_URL + FLEET_NEWS}`, '_blank');
  };

  return (
    <Container fluid className="landing-page-container seafarer-landing-page-container">
      <Row className="justify-content-md-center">
        <Col md={12} lg={4} className="py-3">
          <Card body className="world-clock-card widget-border">
            <h1 className="seafarer-greeting">
              Hello, <br /> {keycloak.tokenParsed?.name || 'Seafarer'}!
            </h1>
            <div className="border-bottom"></div>
            <p className="report-title">World Clock</p>
            <Row className="ml-auto mr-auto">
              {userZoneInfoList.map((ele, ind) => (
                <Col key={ele.city} xs={6} sm={3} md={6} lg={4} xl={3}>
                  <AnalogClock
                    city={ele.city}
                    timezone={ele.timezone}
                    labelClicked={() => {}}
                    ind={ind}
                  />
                </Col>
              ))}
            </Row>
          </Card>

          <Card body className="tolas-card widget-border">
            <div className="tolas-header">
              <img src={tolasIcon} alt="TOLAS Icon" className="tolas-icon" />
              <div className="tolas-title">TOLAS</div>
            </div>
            <p className="tolas-text">
              Seafarer training made easy—learn anywhere. Go to TOLAS to begin.
            </p>
            <Button variant="primary" className="tolas-button" onClick={handleStartTraining}>
              Start Training
            </Button>
          </Card>
        </Col>
        <Col md={12} lg={8} className="py-3">
          <Card body className="news-card widget-border">
            <p className="fleet-news-title">Fleet News</p>
            {loading ? (
              <div className="news-shimmer">
                <div className="major-news shimmer">
                  <div className="news-image shimmer-box"></div>
                  <div className="news-content">
                    <div className="news-category shimmer-text short"></div>
                    <div className="news-date shimmer-text short"></div>
                    <h3 className="news-title shimmer-text medium"></h3>
                    <p className="news-description shimmer-text long"></p>
                    <div className="read-more shimmer-text short"></div>
                  </div>
                </div>
                <Row>
                  {[...Array(4)].map((_, index) => (
                    <Col key={index} xs={12} sm={6} className="news-item">
                      <Card body className="news-card-small widget-border">
                        <Row>
                          <Col xs={4}>
                            <div className="news-image-small shimmer-box"></div>
                          </Col>
                          <Col xs={8} className="card-news-content">
                            <div className="card-news-category shimmer-text short"></div>
                            <h4 className="card-news-title shimmer-text medium"></h4>
                            <div className="card-news-date shimmer-text short"></div>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            ) : newsItems?.length > 0 ? (
              <>
                <div className="major-news">
                  <img src={topNews?.image} alt={topNews?.title} className="news-image" />
                  <div className="news-content">
                    <div className="news-category">{topNews?.category}</div>
                    <div className="news-date">{topNews?.date}</div>
                    <h3 className="news-title">{topNews?.title}</h3>
                    <p className="news-description">{topNews?.description}</p>
                    <div
                      className="read-more"
                      onClick={() =>
                        handleNewsClick(
                          topNews?.id as string,
                          topNews?.category as string,
                          topNews?.href as string,
                        )
                      }
                    >
                      Read More
                    </div>
                  </div>
                </div>
                {!!newsItems?.length && (
                  <Row>
                    {newsItems?.slice(1)?.map(news => (
                      <Col key={news.id} xs={12} sm={6} className="news-item">
                        <Card
                          body
                          className="news-card-small widget-border"
                          onClick={() => handleNewsClick(news.id, news.category, news.href)}
                        >
                          <Row>
                            <Col xs={4}>
                              <img src={news.image} alt={news.title} className="news-image-small" />
                            </Col>
                            <Col xs={8} className="card-news-content">
                              <div className="card-news-category">{news.category}</div>
                              <h4 className="card-news-title">{news.title}</h4>
                              <div className="card-news-date">{news.date}</div>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
                <div className="see-more-container">
                  <div className="see-more-button" onClick={handleSeeMore}>
                    See More
                  </div>
                </div>
              </>
            ) : (
              <p>No news available.</p>
            )}
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export { SeafarerDashboard };
