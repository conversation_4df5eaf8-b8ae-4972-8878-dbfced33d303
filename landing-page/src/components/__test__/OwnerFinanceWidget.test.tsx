import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { OwnerFinanceWidget } from '../dashboard/OwnerFinanceWidget';
import { GlobalContext } from '../../context/dashboard-context';
import { TableauLandingWidgets, DefaultDashboardFilters } from '../../constants/widgets';
import { Keycloak } from '../../types/keycloak';

// Mock hooks before importing them
jest.mock('../../hooks/useTableauToken', () => ({
  useTableauTokens: jest.fn(() => ['test-token']),
}));

jest.mock('../../hooks/useDashboardLoader', () => ({
  useDashboardLoader: jest.fn(() => false),
}));

// Mock the DashboardWrapper component
jest.mock('../common/DashboardWrapper', () => ({
  DashboardWrapper: jest.fn(({ children }) => (
    <div data-testid="mock-dashboard-wrapper">
      <div className="tableau-dashboard">{children}</div>
    </div>
  )),
}));

// Mock StyleGuide
jest.mock('../../StyleGuide', () => ({
  __esModule: true,
  default: {
    Icon: jest.fn(),
    ErrorPage: jest.fn(),
    YoutubeEmbed: jest.fn(),
  },
}));

// Mock react-bootstrap
jest.mock('react-bootstrap', () => ({
  Button: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <button type="button" onClick={onClick}>
      {children}
    </button>
  ),
  Spinner: () => <div role="status" />,
}));

// Create manual mocks for services
jest.mock('../../services/user-service', () => ({
  getUser: jest.fn().mockResolvedValue({ id: 'test-user' }),
}));

jest.mock('../../services/tableau-proxy', () => ({
  getToken: jest.fn().mockResolvedValue('test-token'),
}));

// Mock environment variable
process.env.PARIS_TWO_HOST = 'http://test-host';

describe('OwnerFinanceWidget', () => {
  const mockGa4EventTrigger = jest.fn();

  const mockTokenParsed = {
    email: '<EMAIL>',
    rank: 'test-rank',
    name: 'Test User',
    given_name: 'Test',
    family_name: 'User',
    user_id: 'test-user-id',
    preferred_username: 'testuser',
    group: ['test-group'],
    ip_address: '127.0.0.1',
    resource_access: {
      'paris-two': {
        roles: [] as string[],
      },
    },
  };

  const defaultProps = {
    vesselList: [] as any[],
    keycloak: {
      tokenParsed: mockTokenParsed,
      realmAccess: {
        roles: [],
      },
      idTokenParsed: { ...mockTokenParsed },
    } as Keycloak,
    isLoadingVessel: false,
  };

  const mockContextValue = {
    ga4EventTrigger: mockGa4EventTrigger,
    vesselCurrentlyOwned: [],
    isVesselLoading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Ensure hooks return non-loading state by default
    require('../../hooks/useDashboardLoader').useDashboardLoader.mockReturnValue(false);
    require('../../hooks/useTableauToken').useTableauTokens.mockReturnValue(['test-token']);
  });

  const renderWithContext = (props = defaultProps) => {
    return render(
      <GlobalContext.Provider value={mockContextValue}>
        <OwnerFinanceWidget {...props} />
      </GlobalContext.Provider>,
    );
  };

  it('should render loading spinner when isDashboardLoading is true', () => {
    require('../../hooks/useDashboardLoader').useDashboardLoader.mockReturnValue(true);
    renderWithContext();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('should render loading spinner when isLoadingVessel is true', () => {
    renderWithContext({ ...defaultProps, isLoadingVessel: true });
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('should create correct vessel name filter for empty vessel list', () => {
    renderWithContext();
    const dashboardWrapper = screen.getByTestId('mock-dashboard-wrapper');
    expect(dashboardWrapper).toBeInTheDocument();
  });

  it('should create correct vessel name filter for non-empty vessel list', () => {
    const vessels = [{ name: 'Vessel1' }, { name: 'Vessel2' }] as any[];
    renderWithContext({ ...defaultProps, vesselList: vessels });
    const dashboardWrapper = screen.getByTestId('mock-dashboard-wrapper');
    expect(dashboardWrapper).toBeInTheDocument();
  });

  it('should not show View Dashboard button without OFR access', () => {
    renderWithContext();
    expect(screen.queryByRole('button', { name: 'View Dashboard' })).not.toBeInTheDocument();
  });
});
