import React from 'react';
import { parseISO, format, parse } from 'date-fns';
import { CommentIcon, ExternalLinkIcon, ThreeDotsMenuIcon } from '../VesselWidget/svgIcons';
import { CountBadge, StatusBadge } from 'src/components/common/WidgetBadges';
import { getOfrStatusLabelTheme, getRaStatusLabelTheme } from 'src/utils/utility';
import { Dropdown } from 'react-bootstrap';
import '../styles/column-config.scss';
import { sendUnAddressedCommentsReminder } from 'src/services/vm-widget-service';

export type CellRenderer<T> = (props: { row: { original: T } }) => React.ReactNode;

export type ColumnDef<T> = {
  id?: string;
  accessorKey?: string;
  header?: string | React.ReactNode;
  cell?: CellRenderer<T>;
  meta?: Record<string, any>;
  minSize?: number;
  enableSorting?: boolean;
};

export const getItineraryColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'vessel_name',
    id: 'vessel',
    header: 'Vessel',
    enableSorting: false,
    meta: { isSticky: true, stickySide: 'left' },
    minSize: 200,
  },
  {
    accessorKey: 'last_port',
    id: 'last_port',
    header: 'Last Port',
    enableSorting: false,
    minSize: 140,
  },
  {
    accessorKey: 'port',
    id: 'port',
    header: 'Next Port',
    enableSorting: false,
    minSize: 140,
  },
  {
    accessorKey: 'estimated_arrival',
    id: 'estimated_arrival',
    header: 'ETA',
    cell: ({ row }) => {
      const dateStr = row.original.estimated_arrival;
      if (!dateStr) return '-';
      try {
        const date = parseISO(dateStr);
        return format(date, 'dd MMM yyyy HH:mm');
      } catch {
        return dateStr;
      }
    },
    enableSorting: false,
    minSize: 180,
  },
  {
    id: 'action',
    header: 'Action',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    cell: ({ row }) => {
      const vesselOwnershipId = row.original.vessel_ownership_id;
      const url = `vessel/ownership/details/${vesselOwnershipId}/itinerary`;
      return (
        <div className="action-button-wrapper">
          <button
            onClick={() => {
              window.open(url, '_blank');
            }}
            className="action-button"
          >
            <ExternalLinkIcon height={22} width={22} />
          </button>
        </div>
      );
    },
    enableSorting: false,
    minSize: 80,
  },
];

export const getOfrColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'vessel_name',
    id: 'vessel',
    header: 'Vessel',
    enableSorting: false,
    meta: { isSticky: true, stickySide: 'left' },
    minSize: 180,
  },
  {
    accessorKey: 'month',
    id: 'month',
    header: 'Month',
    enableSorting: false,
    minSize: 80,
  },
  {
    accessorKey: 'status',
    id: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status: string = row.original.status ?? '-';
      const [label, theme] = getOfrStatusLabelTheme(status);
      return (
        <div className="d-flex justify-content-center">
          <StatusBadge text={label} theme={theme} />
        </div>
      );
    },
    meta: { headerAlign: 'center' },
    enableSorting: false,
    minSize: 115,
  },
  {
    accessorKey: 'last_flow_status',
    id: 'review_pending',
    header: 'Pending at',
    enableSorting: false,
    minSize: 110,
  },
  {
    accessorKey: 'variance_percentage',
    id: 'variancePercentage',
    header: 'Variance %',
    enableSorting: false,
    cell: ({ row }) => {
      const variancePercentage = row.original.variance_percentage ?? 0;
      const varianceTheme = variancePercentage > 0 ? 'green' : 'red';
      const varianceText =
        variancePercentage > 0 ? `${variancePercentage}` : `(${Math.abs(variancePercentage)})`;

      return (
        <div className="d-flex justify-content-center">
          <CountBadge text={varianceText} theme={varianceTheme} />
        </div>
      );
    },
    meta: { headerAlign: 'center' },
    minSize: 105,
  },
  {
    id: 'comments_count',
    header: 'Comments',
    cell: ({ row }) => {
      const resolvedComments = row.original.resolved_comments;
      const totalComments = row.original.total_comments;
      return (
        <div>
          <CommentIcon /> {resolvedComments}/{totalComments}
        </div>
      );
    },
    enableSorting: false,
    minSize: 105,
  },
  {
    id: 'action',
    header: 'Action',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    cell: ({ row }) => {
      const ofrId = row.original.id;
      const ownershipId = row.original.vessel_ownership_id;
      const showSendReminder = row.original.resolved_comments !== row.original.total_comments;
      const url = `owner-reporting/${ownershipId}/reports/${ofrId}?activeTab=VA`;

      return (
        <Dropdown className="three-dots-dropdown d-flex align-items-center justify-content-center">
          <Dropdown.Toggle as="div" className="dropdown-toggle-no-caret">
            <ThreeDotsMenuIcon />
          </Dropdown.Toggle>
          <Dropdown.Menu
            className="dropdown-menu-right text-style"
            popperConfig={{
              modifiers: [{ name: 'preventOverflow', options: { boundary: 'viewport' } }],
            }}
          >
            <Dropdown.Item
              onClick={() => {
                window.open(url, '_blank');
              }}
            >
              View Report
            </Dropdown.Item>
            {showSendReminder && (
              <Dropdown.Item
                onClick={() => {
                  sendUnAddressedCommentsReminder(ofrId, ownershipId);
                }}
              >
                Send Reminder
              </Dropdown.Item>
            )}
          </Dropdown.Menu>
        </Dropdown>
      );
    },
    enableSorting: false,
    minSize: 80,
  },
];

export const getRiskAssessmentColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'vessel_name',
    id: 'vessel',
    header: 'Vessel',
    enableSorting: false,
    meta: { isSticky: true, stickySide: 'left' },
    minSize: 100,
  },
  {
    accessorKey: 'task_requiring_ra',
    id: 'task_required',
    header: 'Task Required',
    enableSorting: false,
    minSize: 250,
  },
  {
    accessorKey: 'ra_level',
    id: 'level_of_ra',
    header: 'Level of R.A.',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    enableSorting: false,
    minSize: 100,
    // Use the new functions and component
    cell: ({ row }) => {
      const status = row.original.ra_level;
      const [label, theme] = getRaStatusLabelTheme(status);
      return (
        <div style={{ textAlign: 'center', width: '100%' }}>
          <StatusBadge text={label ?? '-'} theme={theme} />
        </div>
      );
    },
  },
  {
    id: 'action',
    header: 'Action',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    cell: ({ row }) => {
      const riskId = row.original.id;
      const url = `risk-assessment/approval/${riskId}`;
      return (
        <div className="action-button-wrapper">
          <button
            onClick={() => {
              window.open(url, '_blank');
            }}
            className="action-button"
          >
            <ExternalLinkIcon height={22} width={22} />
          </button>
        </div>
      );
    },
    enableSorting: false,
    minSize: 80,
  },
];

export const getDefiniciencyColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'vessel_name',
    id: 'vessel',
    header: 'Vessel/Office',
    enableSorting: false,
    meta: { isSticky: true, stickySide: 'left' },
    minSize: 100,
  },
  {
    accessorKey: 'not_accepted_by_office',
    id: 'not_accepted_by_office',
    header: 'Open Deficiencies',
    enableSorting: false,
    minSize: 180,
  },
  {
    accessorKey: 'overdue', // Placeholder accessor key
    id: 'overdue',
    header: 'Overdue',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    enableSorting: false,
    minSize: 100,
    cell: ({ row }) => {
      return (
        <div className="d-flex justify-content-center">
          <CountBadge text={row.original.overdue} theme={'red'} />
        </div>
      );
    },
  },
  {
    accessorKey: 'due_within_30_days',
    id: 'due_within_30_days',
    header: 'Due within 30 days',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    enableSorting: false,
    minSize: 100,
    cell: ({ row }) => {
      return (
        <div className="d-flex justify-content-center">
          <CountBadge text={row.original.overdue} theme={'yellow'} />
        </div>
      );
    },
  },
  {
    id: 'action',
    header: 'Action',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    cell: () => (
      <div className="action-button-wrapper">
        <button
          // onClick={() => {}}
          className="action-button"
        >
          <ExternalLinkIcon height={22} width={22} />
        </button>
      </div>
    ),
    enableSorting: false,
    minSize: 80,
  },
];

export const getSurveysAndCertsColumns = (activeTab: string = 'all_types'): ColumnDef<any>[] => [
  {
    accessorKey: 'name',
    id: 'vessel',
    header: 'Vessel',
    enableSorting: false,
    meta: { isSticky: true, stickySide: 'left' },
    minSize: 150,
  },
  {
    accessorKey: 'overdue',
    id: 'overdue',
    header: 'Overdue',
    meta: { headerAlign: 'center' },

    cell: ({ row }) => {
      const overdue = row.original.overdue ?? 0;
      return (
        <div style={{ textAlign: 'center', width: '100%' }}>
          <CountBadge text={overdue} theme={'red'} />
        </div>
      );
    },
    enableSorting: false,
    minSize: 80,
  },
  {
    accessorKey: 'due_within_30_days',
    id: 'due_within_30_days',
    header: 'Due within 30 days',
    meta: { headerAlign: 'center' },
    cell: ({ row }) => {
      const dueWithin30Days = row.original.due_within_30_days ?? 0;
      return (
        <div style={{ textAlign: 'center', width: '100%' }}>
          <CountBadge text={dueWithin30Days} theme={'yellow'} />
        </div>
      );
    },
    enableSorting: false,
    minSize: 150,
  },
  {
    accessorKey: 'due_within_60_days',
    id: 'due_within_60_days',
    header: 'Due within 60 days',
    meta: { headerAlign: 'center' },
    cell: ({ row }) => {
      const dueWithin60Days = row.original.due_within_60_days ?? 0;
      return (
        <div style={{ textAlign: 'center', width: '100%' }}>
          <CountBadge text={dueWithin60Days} theme={'green'} />
        </div>
      );
    },
    enableSorting: false,
    minSize: 150,
  },
  {
    id: 'action',
    header: 'Action',
    meta: { isSticky: true, stickySide: 'right', headerAlign: 'center' },
    cell: ({ row }) => {
      const vesselOwnershipId = row.original.vessel_ownership_id;
      const tab = activeTab === 'All' ? 'all_types' : activeTab.toLowerCase();
      const url = `vessel/ownership/details/${vesselOwnershipId}/certificates?activeTab=${tab}`;
      const sendEmailUrl = `vessel/ownership/details/${vesselOwnershipId}/certificates?sendEmail=true`;

      return (
        <Dropdown className="three-dots-dropdown d-flex align-items-center justify-content-center">
          <Dropdown.Toggle as="div" className="dropdown-toggle-no-caret">
            <ThreeDotsMenuIcon />
          </Dropdown.Toggle>
          <Dropdown.Menu
            className="dropdown-menu-right text-style"
            popperConfig={{
              modifiers: [{ name: 'preventOverflow', options: { boundary: 'viewport' } }],
            }}
          >
            <Dropdown.Item
              onClick={() => {
                window.open(url, '_blank');
              }}
            >
              View Surveys and Certs
            </Dropdown.Item>
            <Dropdown.Item
              onClick={() => {
                window.open(sendEmailUrl, '_blank');
              }}
            >
              Send Email
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      );
    },
    enableSorting: false,
    minSize: 150,
  },
];
