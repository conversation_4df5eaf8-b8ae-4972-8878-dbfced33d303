export type GridComponentType = 'bar' | 'pie' | 'dashboard';
export interface Vessel {
  name: string;
  vesselData: any[];
  type: string; // can be custom for defining the different types of tabs
  vessel_ownership_id?: number;
  risk_id?: number | null;
  vessel_id?: number;
}

export interface VesselGroup {
  id: number;
  title: string;
  vessels: VesselOption[];
}

export interface VesselOption {
  vessel_id: number;
  vessel_ownership_id?: number;
  name: string;
  vessel_account_code_new: string;
}
export interface MultiVesselSelectConfig {
  placeholder: string;
  width: string;
  groups: VesselGroup[];
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
}
export interface ChartData {
  openDeficiencies: {
    title: string;
    total: number;
    data: { label: string; value: number; color: string }[];
  };
  closedDeficiencies: {
    title: string;
    total: number;
    data: { label: string; value: number; color: string }[];
  };
}

export interface IProjectListResponse<T> {
  data: T[];
  lastUpdated: string;
  pagination: {
    totalItems: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
   severityData?: any;
}

export interface VesselDisplayProps {
  readonly vessels: Vessel[];
  readonly tableHeaders: string[];
  readonly badgeColors: string[];
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;
  readonly fetchNextPage: () => void;
  readonly pagination: IProjectListResponse<any>['pagination'];
}

export type SortConfig = {
  key: string;
  direction: 'ascending' | 'descending';
} | null;

export interface VesselTableProps extends VesselDisplayProps {
  readonly cellStyleType?: 'default' | 'conditional';
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}

export interface VesselGridProps extends VesselDisplayProps {
  readonly isModal: boolean; // Specific prop for the grid view in a modal
  readonly gridComponent?: GridComponentType;
}

export interface VesselModuleProps {
  readonly title: string;
  readonly vessels: Vessel[];
  readonly tabs: string[];
  readonly tableHeaders: string[];
  readonly badgeColors: string[];
  readonly multiVesselSelects: MultiVesselSelectConfig[];

  // Callbacks
  onRefresh: (options?: { refetch?: boolean }) => void;
  //   onSendEmail: (vesselName: string) => void;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly fetchNextPage: () => void;

  // State flags
  readonly IsiconRenderVisible?: boolean;
  readonly IsenLargeIconVisible?: boolean;
  readonly IsVesselSelectVisible?: boolean;
  readonly IsAllTabVisible?: boolean;
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;

  // Prop to control layout
  readonly vesselSelectPosition?: 'before' | 'after';

  // Sizing

  readonly sizeKey: 'sm' | 'md' | 'lg';

  readonly pagination: IProjectListResponse<any>['pagination'];
  readonly gridComponent?: GridComponentType;
  readonly defaultComponent?: 'list' | 'grid';
  readonly cellStyleType?: 'default' | 'conditional';
  lastUpdated: string;
}

export interface VesselSelectItem {
  groups: VesselGroup[]; // Groups for this select
  selectedVessels: string[]; // Selected vessels for this select
  placeholder?: string;
  width?: string;
}

export type VesselSelectGroupProps = {
  index: number;
  config: {
    placeholder?: string;
    width?: string;
  };
  selectedVessels: string[];
  groups: VesselGroup[];
  onChange: (index: number, selected: string[]) => void;
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
};

export interface VesselDropdownProps {
  readonly groups: VesselGroup[];
  readonly selectedVessels: string[];
  readonly onSelectionChange: (selected: readonly string[]) => void;
  readonly placeholder?: string;
  readonly width?: string;
  readonly isSearchBoxVisible?: boolean;
  readonly isSelectAllVisible?: boolean;
}

export interface VesselGroupListProps {
  filteredGroups: VesselGroup[];
  selectedVessels: string[];
  onToggleVessel: (vesselName: string) => void;
  onToggleGroup: (group: VesselGroup) => void;
}

export interface VesselDropdownMenuProps extends VesselGroupListProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  isAllSelected: boolean;
  onToggleAll: () => void;
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
}

export interface DropdownFooterProps {
  isAllSelected: boolean;
  onToggleAll: () => void;
}

export interface SearchInputProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export interface FetchParams {
  page: number;
  limit: number;
  type?: string;
  [key: string]: any;
}

export interface RiskApprover {
  id: number;
  keycloak_id: string;
  user_name: string;
  user_email: string;
  job_title: string | null;
  message: string | null;
  approval_order: number | null;
  approval_status: number | null;
  approval_date: string | null;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface RiskAssessment {
  id: number;
  vessel_id: number;
  vessel_name: string;
  vessel_ownership_id: number;
  ra_level: number | null | string;
  status: number;
  task_requiring_ra: string;
  risk_approver: RiskApprover[];
}

export interface IRiskAssessmentResponse {
  message: string;
  data: RiskAssessment[];
  last_updated_on: string;
}
export interface TransformedRiskAssessment {
  id: number;
  vessel_id: number;
  vessel_name: string;
  vessel_ownership_id: number;
  ra_level: string | null;
  task_requiring_ra: string;
}

export interface TransformedIRiskAssessmentResponse {
  message: string;
  data: TransformedRiskAssessment[];
  last_updated_on: string;
}

export enum RAStatus {
  DRAFT = 1,
  PENDING = 2,
  APPROVED = 3,
  REJECTED = 4,
  APPROVED_WITH_CONDITION = 5,
}

export enum RaLevel {
  ROUTINE = 1,
  SPECIAL = 2,
  CRITICAL = 3,
  LEVEL_1_RA = 4,
  Unassigned = -1, // handle null with -1 internally
}

export interface TransformedRisk {
  name: string;
  type: keyof typeof RaLevel;
  vesselData: [string, string]; // task, RA level label
  vessel_ownership_id: number;
  risk_id: number | null; // from risk_approver[0]
  vessel_id?: number; // optional, can be used for filtering
}

export interface VesselModuleContainerProps {
  readonly title: string;
  readonly fetchFn: (params: {
    readonly page: number;
    readonly limit: number;
    readonly [key: string]: any;
  }) => Promise<IProjectListResponse<Vessel>>;
  readonly tabs: readonly string[];
  readonly tableHeaders: readonly string[];
  readonly badgeColors: readonly string[];
  readonly sizeKey: 'sm' | 'md' | 'lg';
  readonly IsiconRenderVisible?: boolean;
  readonly IsenLargeIconVisible?: boolean;
  readonly IsVesselSelectVisible?: boolean;
  readonly vesselSelectPosition?: 'before' | 'after';
  readonly gridComponent?: GridComponentType;
  readonly defaultComponent?: 'list' | 'grid';
  readonly cellStyleType?: 'default' | 'conditional';
}

export interface UseInfiniteScrollProps {
  fetchNextPage?: () => void;
  isFetchingNextPage?: boolean;
  hasNextPage: boolean;
  dataLength: number; // Used to re-run checks when new data arrives
}

export interface Itinerary {
  created_at: string;
  updated_at: string;
  vessel_ownership_id: number;
  estimated_arrival: string;
  country: string;
  port: string;
  id: number;
  vessel_id: number;
  vessel_name: string;
  last_port: string;
}

export interface ItineraryApiResponse {
  results: Itinerary[];
  total: number;
}

export interface OfrStat {
  id: number;
  month: string;
  vessel_name: string;
  vessel_code: number;
  vessel_ownership_id: number;
  status: string;
  total_comments: string;
  resolved_comments: string;
  variance: number;
  variance_percentage: number;
  last_flow_status: string;
}

export interface OfrStatsApiResponse {
  results: OfrStat[];
  lastModified: string;
}

export enum CertificateType {
  ANCILLARY = 'ancillary',
  IMPORTANT = 'important',
  STATUTORY = 'statutory',
}

export interface SurveyCertificate {
  certificate_type: CertificateType;
  vessel_id: number;
  ownership_id: number;
  name: string;
  missing_due_date: string;
  overdue: string;
  due_within_30_days: string;
  due_within_60_days: string;
  rest: string;
}
export interface SurveyCertificateApiResponse {
  results: SurveyCertificate[];
  total: number;
  lastModified?: string;
}
// Extended type for table rows
export interface SurveyCertificateWithType extends SurveyCertificate {
  type: 'Statutory' | 'Important' | 'Ancillary';
}

export type Widget = {
  id: number;
  name: string;
  description: string | null;
  is_default: boolean;
  default_position: number;
  status: number;
  type: 'W' | 'C'; // W = Widget, C = Card
};

export type WidgetsApiResponse = {
  message: string;
  count: number;
  results: Widget[];
};
