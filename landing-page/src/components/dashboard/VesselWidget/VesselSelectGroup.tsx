import React, { useContext, useEffect, useCallback, memo } from 'react';
import { VesselSelectGroupProps } from '../../../types/types';
import { VesselDropdown } from './VesselDropdown';
import styles from '../styles/vessel-widget-scss/VesselSelectGroup.module.scss';
import { GlobalContext } from '../../../context/dashboard-context';

/**
 * A simple wrapper component for the VesselDropdown.
 * Wrapped with React.memo because it's a pure component.
 * It will only re-render if its props change.
 */
export const VesselSelectGroup: React.FC<VesselSelectGroupProps> = memo(
  ({
    index,
    config,
    selectedVessels,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible,
  }) => {
    // Hooks are now called unconditionally at the top level
    const { ga4EventTrigger } = useContext(GlobalContext);

    useEffect(() => {
      if (selectedVessels.length > 0 && typeof ga4EventTrigger === 'function') {
        const selectedNames = selectedVessels.join(', ');
        if (index === 0) {
          ga4EventTrigger('Risk Assessment Widget', selectedNames, 'Vessel Selection');
        } else if (index === 1) {
          ga4EventTrigger('Risk Assessment Widget', selectedNames, 'Level of RA Selection');
        }
      }
    }, [selectedVessels, index, ga4EventTrigger]);

    const handleSelectionChange = useCallback(
      (newSelected: readonly string[]) => {
        onChange(index, newSelected as string[]);
      },
      [index, onChange],
    );

    // This conditional check is fine, as it's not a hook call
    if (!groups) {
      console.error('VesselSelectGroup: `groups` prop is missing.');
      return null;
    }

    return (
      <div className={styles.raSelectWrapper}>
        <VesselDropdown
          groups={groups}
          selectedVessels={selectedVessels}
          onSelectionChange={handleSelectionChange}
          placeholder={config.placeholder}
          width={config.width ?? '200px'}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      </div>
    );
  },
);